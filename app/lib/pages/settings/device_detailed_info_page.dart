import 'dart:typed_data';
import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/bluetooth.dart';
import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'components/settings_section.dart';
import 'components/common_settings_tile.dart';

@RoutePage()
class DeviceDetailedInfoPage extends StatefulWidget {
  const DeviceDetailedInfoPage({Key? key}) : super(key: key);

  @override
  State<DeviceDetailedInfoPage> createState() => _DeviceDetailedInfoPageState();
}

class _DeviceDetailedInfoPageState extends State<DeviceDetailedInfoPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<BluetoothServiceBloc>()
            ..add(const BluetoothServiceEvent.checkBluetooth()),
        ),
        BlocProvider(
          create: (context) => getIt<OtaUpdateBloc>(),
        ),
      ],
      child: Builder(
        builder: (context) {
          // Automatically load device firmware info when page opens
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final bluetoothBloc = context.read<BluetoothServiceBloc>();
            final otaBloc = context.read<OtaUpdateBloc>();

            bluetoothBloc.state.maybeWhen(
              connected: (device) {
                otaBloc.add(OtaUpdateEvent.checkDeviceFirmware(device));
              },
              orElse: () {},
            );
          });

          return Scaffold(
              appBar: CurvedAppBar(
                appBarColor: AppTheme.primaryColor,
                logoColor: const Color(0xffFAF2DF),
                height: .35.sw,
              ),
              body: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(25.0),
                  child: Column(
                    children: [
                      SizedBox(height: .35.sw),

                      // Device Connection Status
                      _buildConnectionStatusSection(),

                      const SizedBox(height: 25),

                      // Basic Device Information
                      _buildBasicDeviceInfoSection(),

                      const SizedBox(height: 25),

                      // Detailed Firmware Information
                      _buildDetailedFirmwareInfoSection(),

                      const SizedBox(height: 25),

                      // SMP Protocol Information
                      _buildSmpProtocolInfoSection(),

                      const SizedBox(height: 25),

                      // Device Services Information
                      _buildDeviceServicesSection(),

                      const SizedBox(height: 25),

                      // Test Actions
                      _buildTestActionsSection(),

                      const SizedBox(height: 25),

                      // Firmware Upload Section
                      _buildFirmwareUploadSection(),

                      SizedBox(height: .25.sw),
                    ],
                  ),
                ),
              ));
        },
      ),
    );
  }

  Widget _buildConnectionStatusSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        return SettingsSection(
          title: 'Connection Status',
          children: [
            bluetoothState.maybeWhen(
              connected: (device) => _buildInfoItem(
                icon: Icons.bluetooth_connected,
                iconColor: Colors.green,
                title: 'Connected',
                subtitle: 'Device is connected and ready',
                trailing: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'ONLINE',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              orElse: () => _buildInfoItem(
                icon: Icons.bluetooth_disabled,
                iconColor: Colors.red,
                title: 'Disconnected',
                subtitle: 'No device connected',
                trailing: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'OFFLINE',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBasicDeviceInfoSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        return bluetoothState.maybeWhen(
          connected: (device) => SettingsSection(
            title: 'Basic Device Information',
            children: [
              _buildInfoItem(
                icon: Icons.device_hub,
                title: 'Device Name',
                subtitle: device.platformName.isNotEmpty
                    ? device.platformName
                    : 'Juno Device',
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildInfoItem(
                icon: Icons.fingerprint,
                title: 'Device ID',
                subtitle: device.remoteId.toString(),
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildInfoItem(
                icon: Icons.signal_cellular_alt,
                title: 'Connection Type',
                subtitle: 'Bluetooth Low Energy (BLE)',
              ),
            ],
          ),
          orElse: () => SettingsSection(
            title: 'Basic Device Information',
            children: [
              _buildInfoItem(
                icon: Icons.device_unknown,
                title: 'No Device Connected',
                subtitle: 'Connect a device to view information',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailedFirmwareInfoSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        return bluetoothState.maybeWhen(
          connected: (device) => SettingsSection(
            title: 'Detailed Firmware Information',
            children: [
              BlocBuilder<OtaUpdateBloc, OtaUpdateState>(
                builder: (context, otaState) {
                  return otaState.maybeWhen(
                    firmwareInfoLoaded: (firmwareInfo) => Column(
                      children: [
                        _buildInfoItem(
                          icon: Icons.memory,
                          title: 'Current Firmware Version',
                          subtitle: firmwareInfo.currentVersion,
                        ),
                        const SizedBox(height: 10),
                        const Divider(thickness: 1, height: 7),
                        const SizedBox(height: 10),
                        _buildInfoItem(
                          icon: Icons.computer,
                          title: 'Hardware Version',
                          subtitle: firmwareInfo.hardwareVersion,
                        ),
                        const SizedBox(height: 10),
                        const Divider(thickness: 1, height: 7),
                        const SizedBox(height: 10),
                        _buildInfoItem(
                          icon: Icons.security,
                          title: 'Bootloader',
                          subtitle: firmwareInfo.bootloaderVersion,
                        ),
                        const SizedBox(height: 10),
                        const Divider(thickness: 1, height: 7),
                        const SizedBox(height: 10),
                        _buildInfoItem(
                          icon: Icons.storage,
                          title: 'Available Space',
                          subtitle:
                              '${(firmwareInfo.availableSpace / 1024).toStringAsFixed(0)} KB',
                        ),
                        const SizedBox(height: 10),
                        const Divider(thickness: 1, height: 7),
                        const SizedBox(height: 10),
                        _buildInfoItem(
                          icon: firmwareInfo.supportsOta
                              ? Icons.system_update
                              : Icons.block,
                          iconColor: firmwareInfo.supportsOta
                              ? Colors.green
                              : Colors.red,
                          title: 'OTA Support',
                          subtitle: firmwareInfo.supportsOta
                              ? 'Supported'
                              : 'Not Supported',
                        ),
                      ],
                    ),
                    checkingFirmware: () => _buildInfoItem(
                      icon: Icons.hourglass_empty,
                      title: 'Loading Firmware Information...',
                      subtitle: 'Please wait while we read device details',
                    ),
                    orElse: () => _buildInfoItem(
                      icon: Icons.refresh,
                      title: 'Tap to Load Firmware Info',
                      subtitle: 'Get detailed firmware information from device',
                      onTap: () {
                        context.read<OtaUpdateBloc>().add(
                              OtaUpdateEvent.checkDeviceFirmware(device),
                            );
                      },
                    ),
                  );
                },
              ),
            ],
          ),
          orElse: () => SettingsSection(
            title: 'Detailed Firmware Information',
            children: [
              _buildInfoItem(
                icon: Icons.device_unknown,
                title: 'No Device Connected',
                subtitle: 'Connect a device to view firmware details',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSmpProtocolInfoSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        return bluetoothState.maybeWhen(
          connected: (device) => SettingsSection(
            title: 'SMP Protocol Information',
            children: [
              _buildInfoItem(
                icon: Icons.api,
                title: 'Protocol',
                subtitle: 'Simple Management Protocol (SMP)',
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildInfoItem(
                icon: Icons.library_books,
                title: 'McuMgr Library',
                subtitle: 'Nordic mcumgr_flutter v0.4.2',
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildInfoItem(
                icon: Icons.info_outline,
                title: 'SMP Service UUID',
                subtitle: '8d53dc1d-1db7-4cd3-868b-8a527460aa84',
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildInfoItem(
                icon: Icons.settings_input_antenna,
                title: 'SMP Characteristic UUID',
                subtitle: 'da2e7828-fbce-4e01-ae9e-261174997c48',
              ),
            ],
          ),
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildDeviceServicesSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        return bluetoothState.maybeWhen(
          connected: (device) => SettingsSection(
            title: 'Device Services',
            children: [
              _buildInfoItem(
                icon: Icons.medical_services,
                title: 'Device Information Service',
                subtitle: '0000180a-0000-1000-8000-00805f9b34fb',
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildInfoItem(
                icon: Icons.system_update,
                title: 'SMP Management Service',
                subtitle: '8d53dc1d-1db7-4cd3-868b-8a527460aa84',
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildInfoItem(
                icon: Icons.security,
                title: 'Nordic DFU Service',
                subtitle: '0000fe59-0000-1000-8000-00805f9b34fb',
              ),
            ],
          ),
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildTestActionsSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        return bluetoothState.maybeWhen(
          connected: (device) => SettingsSection(
            title: 'Test Actions',
            children: [
              _buildActionItem(
                icon: Icons.refresh,
                title: 'Refresh Firmware Info',
                subtitle: 'Re-read firmware information from device',
                onTap: () {
                  context.read<OtaUpdateBloc>().add(
                        OtaUpdateEvent.checkDeviceFirmware(device),
                      );
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Refreshing firmware information...'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildActionItem(
                icon: Icons.search,
                title: 'Discover Services',
                subtitle: 'Scan and list all available BLE services',
                onTap: () {
                  _showServicesDialog(context, device);
                },
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildActionItem(
                icon: Icons.bug_report,
                title: 'Test SMP Connection',
                subtitle: 'Test SMP protocol connectivity',
                onTap: () {
                  _testSmpConnection(context, device);
                },
              ),
            ],
          ),
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String subtitle,
    Color? iconColor,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: (iconColor ?? AppTheme.primaryColor).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: iconColor ?? AppTheme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (trailing != null) trailing,
            if (onTap != null && trailing == null)
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return _buildInfoItem(
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
    );
  }

  void _showServicesDialog(BuildContext context, BluetoothDevice device) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Device Services'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: FutureBuilder<List<BluetoothService>>(
            future: device.discoverServices(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(
                  child: Text('Error: ${snapshot.error}'),
                );
              }

              final services = snapshot.data ?? [];

              if (services.isEmpty) {
                return const Center(
                  child: Text('No services found'),
                );
              }

              return ListView.builder(
                itemCount: services.length,
                itemBuilder: (context, index) {
                  final service = services[index];
                  return ExpansionTile(
                    title: Text(
                      'Service ${index + 1}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text(
                      service.uuid.toString(),
                      style: const TextStyle(fontSize: 12),
                    ),
                    children: service.characteristics.map((char) {
                      return ListTile(
                        dense: true,
                        title: Text(
                          'Characteristic',
                          style: const TextStyle(fontSize: 14),
                        ),
                        subtitle: Text(
                          char.uuid.toString(),
                          style: const TextStyle(fontSize: 11),
                        ),
                        trailing: Text(
                          _getCharacteristicProperties(char),
                          style: const TextStyle(fontSize: 10),
                        ),
                      );
                    }).toList(),
                  );
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _getCharacteristicProperties(BluetoothCharacteristic char) {
    final properties = <String>[];
    if (char.properties.read) properties.add('R');
    if (char.properties.write) properties.add('W');
    if (char.properties.notify) properties.add('N');
    if (char.properties.indicate) properties.add('I');
    return properties.join('|');
  }

  void _testSmpConnection(BuildContext context, BluetoothDevice device) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SMP Connection Test'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Testing SMP protocol connectivity...'),
            SizedBox(height: 16),
            Text(
              'This test will attempt to:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('• Find SMP service'),
            Text('• Locate SMP characteristic'),
            Text('• Test basic SMP communication'),
            SizedBox(height: 16),
            Text(
              'Note: Full SMP image list reading is not yet implemented in mcumgr_flutter package.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Trigger firmware info refresh which will test SMP
              context.read<OtaUpdateBloc>().add(
                    OtaUpdateEvent.checkDeviceFirmware(device),
                  );
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content:
                      Text('Testing SMP connection... Check logs for details.'),
                  duration: Duration(seconds: 3),
                ),
              );
            },
            child: const Text('Test'),
          ),
        ],
      ),
    );
  }

  Widget _buildFirmwareUploadSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        return bluetoothState.maybeWhen(
          connected: (device) => SettingsSection(
            title: 'Firmware Upload (Testing)',
            children: [
              _buildInfoItem(
                icon: Icons.info_outline,
                iconColor: Colors.orange,
                title: 'Test Feature',
                subtitle: 'This is for testing firmware upload functionality',
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildFirmwareFileSelector(),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildUploadButton(device),
            ],
          ),
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildFirmwareFileSelector() {
    // Available firmware files
    final firmwareFiles = [
      {
        'name': 'DFU Application v1',
        'path': 'assets/firmware/dfu_application_1.zip',
        'description': 'Standard firmware package'
      },
      {
        'name': 'DFU Application v2',
        'path': 'assets/firmware/dfu_application_2.zip',
        'description': 'Updated firmware package'
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Firmware File:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        ...firmwareFiles
            .map((firmware) => _buildFirmwareFileOption(firmware))
            .toList(),
      ],
    );
  }

  String? _selectedFirmwarePath;

  Widget _buildFirmwareFileOption(Map<String, String> firmware) {
    final isSelected = _selectedFirmwarePath == firmware['path'];

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFirmwarePath = firmware['path'];
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryColor.withOpacity(0.1)
              : Colors.white,
          border: Border.all(
            color: isSelected
                ? AppTheme.primaryColor
                : Colors.grey.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              isSelected
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: isSelected ? AppTheme.primaryColor : Colors.grey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    firmware['name']!,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color:
                          isSelected ? AppTheme.primaryColor : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    firmware['description']!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.file_download,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadButton(BluetoothDevice device) {
    return BlocBuilder<OtaUpdateBloc, OtaUpdateState>(
      builder: (context, otaState) {
        final isUploading = otaState.maybeWhen(
          updateInProgress: (progress) => true,
          preparingUpdate: () => true,
          verifyingFirmware: () => true,
          preparingDevice: () => true,
          orElse: () => false,
        );

        return _buildActionItem(
          icon: isUploading ? Icons.hourglass_empty : Icons.upload,
          title: isUploading
              ? 'Uploading Firmware...'
              : 'Upload Selected Firmware',
          subtitle: _selectedFirmwarePath != null
              ? 'Upload ${_selectedFirmwarePath!.split('/').last} to device'
              : 'Select a firmware file first',
          onTap: _selectedFirmwarePath != null && !isUploading
              ? () => _uploadFirmware(context, device, _selectedFirmwarePath!)
              : () => _showSelectFirmwareDialog(context),
        );
      },
    );
  }

  void _showSelectFirmwareDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Firmware'),
        content: const Text('Please select a firmware file before uploading.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _uploadFirmware(
      BuildContext context, BluetoothDevice device, String firmwarePath) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upload Firmware'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Upload firmware to ${device.platformName}?'),
            const SizedBox(height: 16),
            Text(
              'File: ${firmwarePath.split('/').last}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'Warning: This will update the device firmware. Make sure the device is charged and nearby.',
              style: TextStyle(
                color: Colors.orange,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startFirmwareUpload(context, device, firmwarePath);
            },
            child: const Text('Upload'),
          ),
        ],
      ),
    );
  }

  void _startFirmwareUpload(
      BuildContext context, BluetoothDevice device, String firmwarePath) async {
    try {
      // Load firmware file from assets
      final ByteData data = await rootBundle.load(firmwarePath);
      final Uint8List firmwareData = data.buffer.asUint8List();

      print(
          '📦 [FIRMWARE] Loaded firmware file: ${firmwarePath.split('/').last} (${firmwareData.length} bytes)');

      // Trigger the OTA update with the loaded firmware data
      context.read<OtaUpdateBloc>().add(
            OtaUpdateEvent.startOtaUpdate(device, firmwareData),
          );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Starting firmware upload: ${firmwarePath.split('/').last} (${(firmwareData.length / 1024).toStringAsFixed(1)} KB)'),
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'View Progress',
            onPressed: () {
              // Could navigate to a dedicated upload progress page
            },
          ),
        ),
      );
    } catch (e) {
      print('❌ [FIRMWARE] Error loading firmware file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading firmware file: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }
}
